# This Dockerfile should be built from the backend/ directory
# Usage: docker build -f auth_service/Dockerfile -t auth-service .

FROM python:3.11-slim

WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy shared modules
COPY shared/ ./shared/

# Copy service code
COPY auth_service/ ./auth_service/

# Expose port
EXPOSE 8001

# Run the application
CMD ["python", "-m", "auth_service.main"]
