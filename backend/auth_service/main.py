"""
Authentication Service - User authentication, authorization, and session management.
"""

import sys
import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import FastAPI, HTTPException, status, Query, Header
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from shared.config import AuthServiceSettings
from shared.models import (
    HealthCheck, User, UserCreate, UserSession, LoginRequest, TokenResponse,
    LoginResponse, LogoutRequest, BaseResponse, UserRole,
    RefreshTokenRequest, OAuthLoginRequest, OAuthAuthorizationRequest,
    OAuthAuthorizationResponse
)
from shared.utils import (
    InMemoryStore, generate_id, create_error_response, create_success_response
)
from shared.jwt_utils import (
    create_token_pair, verify_token, hash_password, verify_password,
    jwt_manager
)
from shared.oauth_utils import get_authorization_url, complete_oauth_flow

# Initialize settings
settings = AuthServiceSettings()

# Create FastAPI app
app = FastAPI(
    title="Medhiq - Authentication Service",
    description="User authentication and session management for Medhiq platform",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

# In-memory stores for Sprint 1
users_store = InMemoryStore()
sessions_store = InMemoryStore()

# Initialize with demo users
def initialize_demo_users():
    """Initialize demo users for testing"""
    demo_users = [
        {
            "user_id": "creator-1",
            "email": "<EMAIL>",
            "name": "Demo Creator",
            "role": UserRole.CREATOR,
            "password": "password123"
        },
        {
            "user_id": "buyer-1", 
            "email": "<EMAIL>",
            "name": "Demo Buyer",
            "role": UserRole.BUYER,
            "password": "password123"
        }
    ]
    
    for user_data in demo_users:
        password = user_data.pop("password")
        user = User(
            **user_data,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        users_store.set(f"user:{user.email}", {
            "user": user.dict(),
            "password_hash": hash_password(password)
        })
        users_store.set(f"user_id:{user.user_id}", user.dict())

# Initialize demo users on startup
initialize_demo_users()


@app.get("/health", response_model=HealthCheck)
async def health_check():
    """Health check endpoint"""
    return HealthCheck(service="auth-service")


@app.post("/auth/login", response_model=LoginResponse)
async def login(login_request: LoginRequest) -> LoginResponse:
    """User login endpoint with JWT tokens"""
    # Get user by email
    user_data = users_store.get(f"user:{login_request.email}")
    if not user_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password"
        )

    # Verify password
    if not verify_password(login_request.password, user_data["password_hash"]):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password"
        )

    # Create JWT tokens
    user = User(**user_data["user"])
    tokens = create_token_pair(
        user_id=user.user_id,
        email=user.email,
        role=user.role.value
    )

    return LoginResponse(
        user=user,
        access_token=tokens["access_token"],
        refresh_token=tokens["refresh_token"],
        token_type=tokens["token_type"],
        expires_in=jwt_manager.access_token_expire_minutes * 60,
        message="Login successful"
    )


@app.post("/auth/refresh", response_model=TokenResponse)
async def refresh_token(refresh_request: RefreshTokenRequest) -> TokenResponse:
    """Refresh access token using refresh token"""
    # Verify refresh token
    token_data = verify_token(refresh_request.refresh_token, token_type="refresh")
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired refresh token"
        )

    # Create new token pair
    tokens = create_token_pair(
        user_id=token_data.user_id,
        email=token_data.email,
        role=token_data.role
    )

    return TokenResponse(
        access_token=tokens["access_token"],
        refresh_token=tokens["refresh_token"],
        token_type=tokens["token_type"],
        expires_in=jwt_manager.access_token_expire_minutes * 60,
        message="Token refreshed successfully"
    )


@app.post("/auth/logout")
async def logout(logout_request: LogoutRequest) -> BaseResponse:
    """User logout endpoint"""
    # For JWT tokens, logout is handled client-side by removing the token
    # In a production system, you might want to maintain a blacklist of revoked tokens
    return BaseResponse(message="Logout successful")


@app.get("/auth/me")
async def get_current_user(authorization: str = Header(...)) -> User:
    """Get current user information using JWT token"""
    # Extract token from Authorization header
    if not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authorization header format"
        )

    token = authorization.split(" ")[1]

    # Verify JWT token
    token_data = verify_token(token, token_type="access")
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token"
        )

    # Get full user data
    user_data = users_store.get(f"user_id:{token_data.user_id}")
    if not user_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return User(**user_data)


@app.get("/users/me")
async def get_user_profile(session_id: str) -> User:
    """Get user profile (alias for get_current_user)"""
    return await get_current_user(session_id)


@app.post("/auth/register")
async def register(user_create: UserCreate) -> User:
    """User registration endpoint"""
    # Check if user already exists
    if users_store.exists(f"user:{user_create.email}"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User with this email already exists"
        )
    
    # Create new user
    user_id = generate_id()
    user = User(
        user_id=user_id,
        email=user_create.email,
        name=user_create.name,
        role=user_create.role,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    
    # Store user with password hash
    password_hash = hash_password(user_create.password or "defaultpassword")
    users_store.set(f"user:{user.email}", {
        "user": user.dict(),
        "password_hash": password_hash
    })
    users_store.set(f"user_id:{user.user_id}", user.dict())
    
    return user


# OAuth Endpoints
@app.post("/auth/oauth/authorize", response_model=OAuthAuthorizationResponse)
async def oauth_authorize(request: OAuthAuthorizationRequest) -> OAuthAuthorizationResponse:
    """Get OAuth authorization URL"""
    try:
        auth_data = get_authorization_url(request.provider)
        return OAuthAuthorizationResponse(
            auth_url=auth_data["auth_url"],
            state=auth_data["state"],
            message="Authorization URL generated successfully"
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@app.post("/auth/oauth/login", response_model=LoginResponse)
async def oauth_login(request: OAuthLoginRequest) -> LoginResponse:
    """Complete OAuth login flow"""
    try:
        # Complete OAuth flow
        user_info = await complete_oauth_flow(request.provider, request.code, request.state)
        if not user_info:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="OAuth authentication failed"
            )

        # Check if user exists
        user_data = users_store.get(f"user:{user_info.email}")

        if user_data:
            # Existing user
            user = User(**user_data["user"])
        else:
            # Create new user
            user_id = generate_id()
            user = User(
                user_id=user_id,
                email=user_info.email,
                name=user_info.name,
                role=UserRole.BUYER,  # Default role for OAuth users
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )

            # Store user (no password for OAuth users)
            users_store.set(f"user:{user.email}", {
                "user": user.dict(),
                "password_hash": None,  # OAuth users don't have passwords
                "oauth_provider": user_info.provider,
                "oauth_id": user_info.id
            })
            users_store.set(f"user_id:{user.user_id}", user.dict())

        # Create JWT tokens
        tokens = create_token_pair(
            user_id=user.user_id,
            email=user.email,
            role=user.role.value
        )

        return LoginResponse(
            user=user,
            access_token=tokens["access_token"],
            refresh_token=tokens["refresh_token"],
            token_type=tokens["token_type"],
            expires_in=jwt_manager.access_token_expire_minutes * 60,
            message="OAuth login successful"
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"OAuth login failed: {str(e)}"
        )


@app.get("/auth/sessions/validate")
async def validate_session(session_id: str) -> dict:
    """Validate a session (for other services)"""
    session_data = sessions_store.get(f"session:{session_id}")
    if not session_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired session"
        )
    
    session = UserSession(**session_data)
    return {
        "valid": True,
        "user_id": session.user_id,
        "role": session.role,
        "email": session.email
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )
