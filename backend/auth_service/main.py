"""
Authentication Service - User authentication, authorization, and session management.
"""

import sys
import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import FastAPI, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from shared.config import AuthServiceSettings
from shared.models import (
    HealthCheck, User, UserCreate, UserSession, LoginRequest, 
    LoginResponse, LogoutRequest, BaseResponse, UserRole
)
from shared.utils import (
    InMemoryStore, generate_id, hash_password, verify_password, 
    generate_session_id, create_error_response, create_success_response
)

# Initialize settings
settings = AuthServiceSettings()

# Create FastAPI app
app = FastAPI(
    title="Medhiq - Authentication Service",
    description="User authentication and session management for Medhiq platform",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

# In-memory stores for Sprint 1
users_store = InMemoryStore()
sessions_store = InMemoryStore()

# Initialize with demo users
def initialize_demo_users():
    """Initialize demo users for testing"""
    demo_users = [
        {
            "user_id": "creator-1",
            "email": "<EMAIL>",
            "name": "Demo Creator",
            "role": UserRole.CREATOR,
            "password": "password123"
        },
        {
            "user_id": "buyer-1", 
            "email": "<EMAIL>",
            "name": "Demo Buyer",
            "role": UserRole.BUYER,
            "password": "password123"
        }
    ]
    
    for user_data in demo_users:
        password = user_data.pop("password")
        user = User(
            **user_data,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        users_store.set(f"user:{user.email}", {
            "user": user.dict(),
            "password_hash": hash_password(password)
        })
        users_store.set(f"user_id:{user.user_id}", user.dict())

# Initialize demo users on startup
initialize_demo_users()


@app.get("/health", response_model=HealthCheck)
async def health_check():
    """Health check endpoint"""
    return HealthCheck(service="auth-service")


@app.post("/auth/login")
async def login(login_request: LoginRequest) -> LoginResponse:
    """User login endpoint"""
    # Get user by email
    user_data = users_store.get(f"user:{login_request.email}")
    if not user_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password"
        )
    
    # Verify password
    if not verify_password(login_request.password, user_data["password_hash"]):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password"
        )
    
    # Create session
    user = User(**user_data["user"])
    session_id = generate_session_id()
    session = UserSession(
        user_id=user.user_id,
        email=user.email,
        name=user.name,
        role=user.role,
        created_at=datetime.utcnow(),
        last_accessed=datetime.utcnow()
    )
    
    # Store session with 24 hour TTL
    sessions_store.set(f"session:{session_id}", session.dict(), ttl=86400)
    
    return LoginResponse(
        user=user,
        session_id=session_id,
        message="Login successful"
    )


@app.post("/auth/logout")
async def logout(logout_request: LogoutRequest) -> BaseResponse:
    """User logout endpoint"""
    # Remove session
    sessions_store.delete(f"session:{logout_request.session_id}")
    
    return BaseResponse(message="Logout successful")


@app.get("/auth/me")
async def get_current_user(session_id: str) -> User:
    """Get current user from session"""
    session_data = sessions_store.get(f"session:{session_id}")
    if not session_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired session"
        )
    
    session = UserSession(**session_data)
    
    # Update last accessed time
    session.last_accessed = datetime.utcnow()
    sessions_store.set(f"session:{session_id}", session.dict(), ttl=86400)
    
    # Get full user data
    user_data = users_store.get(f"user_id:{session.user_id}")
    if not user_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return User(**user_data)


@app.get("/users/me")
async def get_user_profile(session_id: str) -> User:
    """Get user profile (alias for get_current_user)"""
    return await get_current_user(session_id)


@app.post("/auth/register")
async def register(user_create: UserCreate) -> User:
    """User registration endpoint"""
    # Check if user already exists
    if users_store.exists(f"user:{user_create.email}"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User with this email already exists"
        )
    
    # Create new user
    user_id = generate_id()
    user = User(
        user_id=user_id,
        email=user_create.email,
        name=user_create.name,
        role=user_create.role,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    
    # Store user with password hash
    password_hash = hash_password(user_create.password or "defaultpassword")
    users_store.set(f"user:{user.email}", {
        "user": user.dict(),
        "password_hash": password_hash
    })
    users_store.set(f"user_id:{user.user_id}", user.dict())
    
    return user


@app.get("/auth/sessions/validate")
async def validate_session(session_id: str) -> dict:
    """Validate a session (for other services)"""
    session_data = sessions_store.get(f"session:{session_id}")
    if not session_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired session"
        )
    
    session = UserSession(**session_data)
    return {
        "valid": True,
        "user_id": session.user_id,
        "role": session.role,
        "email": session.email
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )
