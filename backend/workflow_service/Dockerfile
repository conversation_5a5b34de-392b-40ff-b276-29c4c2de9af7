# This Dockerfile should be built from the backend/ directory
# Usage: docker build -f workflow_service/Dockerfile -t workflow-service .

FROM python:3.11-slim

WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy shared modules
COPY shared/ ./shared/

# Copy service code
COPY workflow_service/ ./workflow_service/

# Set Python path to include the app directory
ENV PYTHONPATH=/app

# Expose port
EXPOSE 8003

# Run the application
CMD ["python", "workflow_service/main.py"]
