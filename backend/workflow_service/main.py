"""
Workflow Service - n8n workflow management, validation, and basic operations.
"""

import sys
import os
from datetime import datetime
from typing import List, Optional, Dict, Any

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import Fast<PERSON><PERSON>, HTTPException, status, Header
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from shared.config import WorkflowServiceSettings
from shared.models import (
    HealthCheck, Workflow, WorkflowCreate, WorkflowUpdate,
    WorkflowStatus, UserRole, WorkflowUploadRequest, WorkflowUploadResponse,
    WorkflowValidationRequest, WorkflowValidationResponse
)
from shared.utils import (
    InMemoryStore, ServiceClient, generate_id,
    create_error_response, create_success_response
)
from shared.n8n_utils import (
    n8n_manager, validate_workflow_json, create_workflow as create_n8n_workflow,
    health_check as n8n_health_check
)
from shared.jwt_utils import verify_token

# Initialize settings
settings = WorkflowServiceSettings()

# Create FastAPI app
app = FastAPI(
    title="Medhiq - Workflow Service",
    description="n8n workflow management and validation for Medhiq platform",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

# In-memory store for Sprint 1
workflows_store = InMemoryStore()

# Auth service client for session validation
auth_client = ServiceClient(settings.auth_service_url)


async def validate_session(session_id: str) -> Dict[str, Any]:
    """Validate session with auth service"""
    try:
        response = await auth_client.get(f"/auth/sessions/validate?session_id={session_id}")
        return response
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired session"
        )


def validate_workflow_json(workflow_json: Dict[str, Any]) -> bool:
    """Basic validation of n8n workflow JSON structure"""
    required_fields = ["nodes", "connections"]
    
    if not isinstance(workflow_json, dict):
        return False
    
    for field in required_fields:
        if field not in workflow_json:
            return False
    
    # Validate nodes structure
    nodes = workflow_json.get("nodes", [])
    if not isinstance(nodes, list):
        return False
    
    # Each node should have required fields
    for node in nodes:
        if not isinstance(node, dict):
            return False
        if "id" not in node or "type" not in node:
            return False
    
    # Validate connections structure
    connections = workflow_json.get("connections", {})
    if not isinstance(connections, dict):
        return False
    
    return True


@app.get("/health", response_model=HealthCheck)
async def health_check():
    """Health check endpoint"""
    dependencies = {}

    # Check n8n connection
    try:
        n8n_healthy = await n8n_health_check()
        dependencies["n8n"] = "healthy" if n8n_healthy else "unhealthy"
    except Exception:
        dependencies["n8n"] = "unhealthy"

    return HealthCheck(
        service="workflow-service",
        dependencies=dependencies
    )


@app.get("/workflows")
async def list_workflows(session_id: Optional[str] = None):
    """List workflows (filtered by user if session provided)"""
    all_workflows = []
    
    # Get user info if session provided
    user_info = None
    if session_id:
        user_info = await validate_session(session_id)
    
    for key in workflows_store._data.keys():
        if key.startswith("workflow:"):
            workflow_data = workflows_store.get(key)
            if workflow_data:
                workflow = Workflow(**workflow_data)
                
                # If user session provided, filter by creator
                if user_info:
                    if user_info["role"] == UserRole.CREATOR:
                        # Creators see only their workflows
                        if workflow.creator_id != user_info["user_id"]:
                            continue
                    else:
                        # Buyers see only published workflows
                        if workflow.status != WorkflowStatus.PUBLISHED:
                            continue
                else:
                    # No session - show only published workflows
                    if workflow.status != WorkflowStatus.PUBLISHED:
                        continue
                
                all_workflows.append(workflow.dict())
    
    # Sort by creation date (newest first)
    all_workflows.sort(key=lambda x: x["created_at"], reverse=True)
    
    return create_success_response(all_workflows)


@app.post("/workflows")
async def create_workflow(workflow_data: Dict[str, Any]):
    """Create a new workflow"""
    session_id = workflow_data.get("session_id")
    if not session_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Session ID required"
        )
    
    # Validate session and get user info
    user_info = await validate_session(session_id)
    
    # Only creators can upload workflows
    if user_info["role"] != UserRole.CREATOR:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only creators can upload workflows"
        )
    
    # Extract workflow data
    workflow_create = WorkflowCreate(
        name=workflow_data["name"],
        description=workflow_data["description"],
        category=workflow_data["category"],
        price=workflow_data.get("price", 0.0),
        tags=workflow_data.get("tags", []),
        workflow_json=workflow_data["workflow_json"]
    )
    
    # Validate workflow JSON
    if not validate_workflow_json(workflow_create.workflow_json):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid workflow JSON structure"
        )
    
    # Create workflow
    workflow_id = generate_id()
    workflow = Workflow(
        workflow_id=workflow_id,
        creator_id=user_info["user_id"],
        name=workflow_create.name,
        description=workflow_create.description,
        category=workflow_create.category,
        price=workflow_create.price,
        tags=workflow_create.tags,
        status=WorkflowStatus.DRAFT,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    
    # Store workflow
    workflow_data_to_store = workflow.dict()
    workflow_data_to_store["workflow_json"] = workflow_create.workflow_json
    workflows_store.set(f"workflow:{workflow_id}", workflow_data_to_store)
    
    return create_success_response(
        workflow.dict(),
        "Workflow created successfully"
    )


@app.post("/workflows/upload", response_model=WorkflowUploadResponse)
async def upload_workflow(
    upload_request: WorkflowUploadRequest,
    authorization: str = Header(...)
) -> WorkflowUploadResponse:
    """Upload and validate n8n workflow"""
    # Verify JWT token
    if not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authorization header format"
        )

    token = authorization.split(" ")[1]
    token_data = verify_token(token, token_type="access")
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token"
        )

    # Validate workflow JSON
    validation_result = validate_workflow_json(upload_request.workflow_json)
    if not validation_result.is_valid:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Workflow validation failed: {', '.join(validation_result.errors)}"
        )

    # Create workflow in n8n
    n8n_result = await create_n8n_workflow(upload_request.workflow_json)
    n8n_workflow_id = None

    if n8n_result and n8n_result.get("success"):
        n8n_workflow_id = n8n_result.get("workflow", {}).get("id")

    # Create workflow record
    workflow_id = generate_id()
    workflow = Workflow(
        workflow_id=workflow_id,
        name=upload_request.name,
        description=upload_request.description,
        category=upload_request.category,
        price=upload_request.price,
        tags=upload_request.tags,
        creator_id=token_data.user_id,
        status=WorkflowStatus.DRAFT,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )

    # Store workflow
    workflows_store.set(f"workflow:{workflow_id}", workflow.dict())

    # Store workflow JSON separately
    workflows_store.set(f"workflow_json:{workflow_id}", {
        "workflow_json": upload_request.workflow_json,
        "n8n_workflow_id": n8n_workflow_id,
        "validation_result": validation_result.dict()
    })

    return WorkflowUploadResponse(
        workflow_id=workflow_id,
        n8n_workflow_id=n8n_workflow_id,
        validation_result=validation_result.dict(),
        message="Workflow uploaded successfully"
    )


@app.post("/workflows/validate", response_model=WorkflowValidationResponse)
async def validate_workflow_endpoint(
    validation_request: WorkflowValidationRequest
) -> WorkflowValidationResponse:
    """Validate n8n workflow JSON without uploading"""
    validation_result = validate_workflow_json(validation_request.workflow_json)

    return WorkflowValidationResponse(
        is_valid=validation_result.is_valid,
        errors=validation_result.errors,
        warnings=validation_result.warnings,
        node_count=validation_result.node_count,
        connection_count=validation_result.connection_count,
        message="Workflow validation completed"
    )


@app.get("/workflows/{workflow_id}")
async def get_workflow(workflow_id: str, session_id: Optional[str] = None):
    """Get workflow details"""
    workflow_data = workflows_store.get(f"workflow:{workflow_id}")
    if not workflow_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow not found"
        )
    
    workflow = Workflow(**{k: v for k, v in workflow_data.items() if k != "workflow_json"})
    
    # Check access permissions
    if session_id:
        user_info = await validate_session(session_id)
        
        # Creators can see their own workflows regardless of status
        if user_info["role"] == UserRole.CREATOR and workflow.creator_id == user_info["user_id"]:
            return create_success_response(workflow_data)
    
    # For non-creators or no session, only show published workflows
    if workflow.status != WorkflowStatus.PUBLISHED:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow not found"
        )
    
    # Don't expose workflow JSON to non-creators
    response_data = workflow.dict()
    return create_success_response(response_data)


@app.put("/workflows/{workflow_id}")
async def update_workflow(workflow_id: str, update_data: Dict[str, Any]):
    """Update workflow"""
    session_id = update_data.get("session_id")
    if not session_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Session ID required"
        )
    
    # Validate session
    user_info = await validate_session(session_id)
    
    # Get existing workflow
    workflow_data = workflows_store.get(f"workflow:{workflow_id}")
    if not workflow_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow not found"
        )
    
    workflow = Workflow(**{k: v for k, v in workflow_data.items() if k != "workflow_json"})
    
    # Check ownership
    if workflow.creator_id != user_info["user_id"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only update your own workflows"
        )
    
    # Update workflow
    update_fields = WorkflowUpdate(**{k: v for k, v in update_data.items() if k != "session_id"})
    
    for field, value in update_fields.dict(exclude_unset=True).items():
        setattr(workflow, field, value)
    
    workflow.updated_at = datetime.utcnow()
    
    # Store updated workflow
    updated_data = workflow.dict()
    updated_data["workflow_json"] = workflow_data["workflow_json"]  # Preserve JSON
    workflows_store.set(f"workflow:{workflow_id}", updated_data)
    
    return create_success_response(
        workflow.dict(),
        "Workflow updated successfully"
    )


@app.post("/workflows/{workflow_id}/publish")
async def publish_workflow(workflow_id: str, request_data: Dict[str, Any]):
    """Publish a workflow to the marketplace"""
    session_id = request_data.get("session_id")
    if not session_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Session ID required"
        )
    
    # Validate session
    user_info = await validate_session(session_id)
    
    # Get workflow
    workflow_data = workflows_store.get(f"workflow:{workflow_id}")
    if not workflow_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow not found"
        )
    
    workflow = Workflow(**{k: v for k, v in workflow_data.items() if k != "workflow_json"})
    
    # Check ownership
    if workflow.creator_id != user_info["user_id"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only publish your own workflows"
        )
    
    # Update status to published
    workflow.status = WorkflowStatus.PUBLISHED
    workflow.updated_at = datetime.utcnow()
    
    # Store updated workflow
    updated_data = workflow.dict()
    updated_data["workflow_json"] = workflow_data["workflow_json"]
    workflows_store.set(f"workflow:{workflow_id}", updated_data)
    
    return create_success_response(
        workflow.dict(),
        "Workflow published successfully"
    )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )
