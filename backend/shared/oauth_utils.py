"""
OAuth Integration Utilities for Medhiq Platform
"""

import os
import secrets
from typing import Optional, Dict, Any
from urllib.parse import urlencode
import httpx
from authlib.integrations.starlette_client import OAuth
from authlib.integrations.base_client import OAuthError
from pydantic import BaseModel


class OAuthUserInfo(BaseModel):
    """OAuth user information model"""
    id: str
    email: str
    name: str
    picture: Optional[str] = None
    provider: str


class GoogleOAuthManager:
    """Google OAuth 2.0 Manager"""
    
    def __init__(self):
        self.client_id = os.getenv("GOOGLE_CLIENT_ID")
        self.client_secret = os.getenv("GOOGLE_CLIENT_SECRET")
        self.redirect_uri = os.getenv("OAUTH_REDIRECT_URI", "http://localhost:3000/auth/callback")
        self.scope = "openid email profile"
        
        # Google OAuth endpoints
        self.auth_url = "https://accounts.google.com/o/oauth2/auth"
        self.token_url = "https://oauth2.googleapis.com/token"
        self.userinfo_url = "https://www.googleapis.com/oauth2/v2/userinfo"
    
    def get_authorization_url(self, state: Optional[str] = None) -> Dict[str, str]:
        """Generate Google OAuth authorization URL"""
        if not state:
            state = secrets.token_urlsafe(32)
        
        params = {
            "client_id": self.client_id,
            "redirect_uri": self.redirect_uri,
            "scope": self.scope,
            "response_type": "code",
            "state": state,
            "access_type": "offline",
            "prompt": "consent"
        }
        
        auth_url = f"{self.auth_url}?{urlencode(params)}"
        
        return {
            "auth_url": auth_url,
            "state": state
        }
    
    async def exchange_code_for_token(self, code: str, state: str) -> Optional[Dict[str, Any]]:
        """Exchange authorization code for access token"""
        try:
            async with httpx.AsyncClient() as client:
                data = {
                    "client_id": self.client_id,
                    "client_secret": self.client_secret,
                    "code": code,
                    "grant_type": "authorization_code",
                    "redirect_uri": self.redirect_uri
                }
                
                response = await client.post(self.token_url, data=data)
                response.raise_for_status()
                
                return response.json()
                
        except Exception as e:
            print(f"Error exchanging code for token: {e}")
            return None
    
    async def get_user_info(self, access_token: str) -> Optional[OAuthUserInfo]:
        """Get user information from Google"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {"Authorization": f"Bearer {access_token}"}
                response = await client.get(self.userinfo_url, headers=headers)
                response.raise_for_status()
                
                user_data = response.json()
                
                return OAuthUserInfo(
                    id=user_data.get("id"),
                    email=user_data.get("email"),
                    name=user_data.get("name"),
                    picture=user_data.get("picture"),
                    provider="google"
                )
                
        except Exception as e:
            print(f"Error getting user info: {e}")
            return None
    
    async def complete_oauth_flow(self, code: str, state: str) -> Optional[OAuthUserInfo]:
        """Complete the OAuth flow and return user info"""
        # Exchange code for token
        token_data = await self.exchange_code_for_token(code, state)
        if not token_data:
            return None
        
        access_token = token_data.get("access_token")
        if not access_token:
            return None
        
        # Get user information
        user_info = await self.get_user_info(access_token)
        return user_info


class OAuthManager:
    """Main OAuth manager supporting multiple providers"""
    
    def __init__(self):
        self.google = GoogleOAuthManager()
        self.supported_providers = ["google"]
    
    def get_provider_manager(self, provider: str):
        """Get OAuth manager for specific provider"""
        if provider == "google":
            return self.google
        else:
            raise ValueError(f"Unsupported OAuth provider: {provider}")
    
    def get_authorization_url(self, provider: str, state: Optional[str] = None) -> Dict[str, str]:
        """Get authorization URL for provider"""
        manager = self.get_provider_manager(provider)
        return manager.get_authorization_url(state)
    
    async def complete_oauth_flow(self, provider: str, code: str, state: str) -> Optional[OAuthUserInfo]:
        """Complete OAuth flow for provider"""
        manager = self.get_provider_manager(provider)
        return await manager.complete_oauth_flow(code, state)


# Global OAuth manager instance
oauth_manager = OAuthManager()


def get_authorization_url(provider: str, state: Optional[str] = None) -> Dict[str, str]:
    """Convenience function to get authorization URL"""
    return oauth_manager.get_authorization_url(provider, state)


async def complete_oauth_flow(provider: str, code: str, state: str) -> Optional[OAuthUserInfo]:
    """Convenience function to complete OAuth flow"""
    return await oauth_manager.complete_oauth_flow(provider, code, state)
