"""
n8n Integration Utilities for Medhiq Platform
"""

import os
import json
from typing import Optional, Dict, Any, List
import httpx
from pydantic import BaseModel, ValidationError


class N8nWorkflow(BaseModel):
    """n8n workflow model"""
    id: Optional[str] = None
    name: str
    nodes: List[Dict[str, Any]]
    connections: Dict[str, Any]
    active: bool = False
    settings: Optional[Dict[str, Any]] = None
    staticData: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    meta: Optional[Dict[str, Any]] = None


class N8nWorkflowValidationResult(BaseModel):
    """Workflow validation result"""
    is_valid: bool
    errors: List[str] = []
    warnings: List[str] = []
    node_count: int = 0
    connection_count: int = 0


class N8nManager:
    """n8n API Manager"""
    
    def __init__(self):
        self.base_url = os.getenv("N8N_BASE_URL", "http://localhost:5678")
        self.api_key = os.getenv("N8N_API_KEY")
        self.webhook_url = os.getenv("N8N_WEBHOOK_URL", f"{self.base_url}/webhook")
        
        # Remove trailing slash from base URL
        if self.base_url.endswith("/"):
            self.base_url = self.base_url[:-1]
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers for n8n API requests"""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        if self.api_key:
            headers["X-N8N-API-KEY"] = self.api_key
        
        return headers
    
    async def health_check(self) -> bool:
        """Check if n8n instance is healthy"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/healthz",
                    headers=self._get_headers(),
                    timeout=10.0
                )
                return response.status_code == 200
        except Exception:
            return False
    
    def validate_workflow_json(self, workflow_data: Dict[str, Any]) -> N8nWorkflowValidationResult:
        """Validate n8n workflow JSON structure"""
        errors = []
        warnings = []
        
        # Check required fields
        required_fields = ["name", "nodes", "connections"]
        for field in required_fields:
            if field not in workflow_data:
                errors.append(f"Missing required field: {field}")
        
        # Validate nodes
        nodes = workflow_data.get("nodes", [])
        if not isinstance(nodes, list):
            errors.append("'nodes' must be a list")
        else:
            for i, node in enumerate(nodes):
                if not isinstance(node, dict):
                    errors.append(f"Node {i} must be an object")
                    continue
                
                # Check required node fields
                node_required = ["name", "type", "position"]
                for field in node_required:
                    if field not in node:
                        errors.append(f"Node {i} missing required field: {field}")
                
                # Check node type
                if "type" in node and not isinstance(node["type"], str):
                    errors.append(f"Node {i} 'type' must be a string")
        
        # Validate connections
        connections = workflow_data.get("connections", {})
        if not isinstance(connections, dict):
            errors.append("'connections' must be an object")
        
        # Count connections
        connection_count = 0
        if isinstance(connections, dict):
            for node_connections in connections.values():
                if isinstance(node_connections, dict):
                    for output_connections in node_connections.values():
                        if isinstance(output_connections, list):
                            connection_count += len(output_connections)
        
        # Warnings for best practices
        if len(nodes) == 0:
            warnings.append("Workflow has no nodes")
        
        if len(nodes) > 50:
            warnings.append("Workflow has many nodes (>50), consider breaking it down")
        
        return N8nWorkflowValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            node_count=len(nodes),
            connection_count=connection_count
        )
    
    async def create_workflow(self, workflow_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create workflow in n8n"""
        try:
            # Validate workflow first
            validation = self.validate_workflow_json(workflow_data)
            if not validation.is_valid:
                return {
                    "success": False,
                    "error": "Workflow validation failed",
                    "validation_errors": validation.errors
                }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/api/v1/workflows",
                    headers=self._get_headers(),
                    json=workflow_data,
                    timeout=30.0
                )
                
                if response.status_code == 201:
                    return {
                        "success": True,
                        "workflow": response.json(),
                        "validation": validation.dict()
                    }
                else:
                    return {
                        "success": False,
                        "error": f"n8n API error: {response.status_code}",
                        "details": response.text
                    }
                    
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to create workflow: {str(e)}"
            }
    
    async def get_workflow(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get workflow from n8n"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/api/v1/workflows/{workflow_id}",
                    headers=self._get_headers(),
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    return None
                    
        except Exception:
            return None
    
    async def list_workflows(self) -> List[Dict[str, Any]]:
        """List all workflows from n8n"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/api/v1/workflows",
                    headers=self._get_headers(),
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return data.get("data", [])
                else:
                    return []
                    
        except Exception:
            return []
    
    async def activate_workflow(self, workflow_id: str) -> bool:
        """Activate workflow in n8n"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/api/v1/workflows/{workflow_id}/activate",
                    headers=self._get_headers(),
                    timeout=10.0
                )
                
                return response.status_code == 200
                
        except Exception:
            return False
    
    async def deactivate_workflow(self, workflow_id: str) -> bool:
        """Deactivate workflow in n8n"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/api/v1/workflows/{workflow_id}/deactivate",
                    headers=self._get_headers(),
                    timeout=10.0
                )
                
                return response.status_code == 200
                
        except Exception:
            return False


# Global n8n manager instance
n8n_manager = N8nManager()


async def health_check() -> bool:
    """Convenience function to check n8n health"""
    return await n8n_manager.health_check()


def validate_workflow_json(workflow_data: Dict[str, Any]) -> N8nWorkflowValidationResult:
    """Convenience function to validate workflow JSON"""
    return n8n_manager.validate_workflow_json(workflow_data)


async def create_workflow(workflow_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Convenience function to create workflow"""
    return await n8n_manager.create_workflow(workflow_data)
