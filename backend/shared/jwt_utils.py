"""
JWT Token Management Utilities for Medhiq Platform
"""

import os
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any, Union
import jwt
from jwt.exceptions import InvalidTokenError
from passlib.context import CryptContext
from pydantic import BaseModel


class TokenData(BaseModel):
    """Token data model"""
    user_id: str
    email: str
    role: str
    token_type: str  # "access" or "refresh"
    exp: datetime
    iat: datetime


class JWTManager:
    """JWT Token Manager for authentication"""
    
    def __init__(self):
        self.secret_key = os.getenv("JWT_SECRET_KEY", "dev-secret-key-change-in-production")
        self.algorithm = os.getenv("JWT_ALGORITHM", "HS256")
        self.access_token_expire_minutes = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
        self.refresh_token_expire_days = int(os.getenv("JWT_REFRESH_TOKEN_EXPIRE_DAYS", "7"))
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    def create_access_token(self, user_id: str, email: str, role: str) -> str:
        """Create JWT access token"""
        now = datetime.now(timezone.utc)
        expire = now + timedelta(minutes=self.access_token_expire_minutes)
        
        payload = {
            "user_id": user_id,
            "email": email,
            "role": role,
            "token_type": "access",
            "exp": expire,
            "iat": now,
            "sub": user_id  # Subject (user identifier)
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def create_refresh_token(self, user_id: str, email: str, role: str) -> str:
        """Create JWT refresh token"""
        now = datetime.now(timezone.utc)
        expire = now + timedelta(days=self.refresh_token_expire_days)
        
        payload = {
            "user_id": user_id,
            "email": email,
            "role": role,
            "token_type": "refresh",
            "exp": expire,
            "iat": now,
            "sub": user_id
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str, token_type: str = "access") -> Optional[TokenData]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Verify token type
            if payload.get("token_type") != token_type:
                return None
            
            # Extract token data
            token_data = TokenData(
                user_id=payload.get("user_id"),
                email=payload.get("email"),
                role=payload.get("role"),
                token_type=payload.get("token_type"),
                exp=datetime.fromtimestamp(payload.get("exp"), tz=timezone.utc),
                iat=datetime.fromtimestamp(payload.get("iat"), tz=timezone.utc)
            )
            
            return token_data
            
        except InvalidTokenError:
            return None
        except Exception:
            return None
    
    def refresh_access_token(self, refresh_token: str) -> Optional[Dict[str, str]]:
        """Create new access token from refresh token"""
        token_data = self.verify_token(refresh_token, token_type="refresh")
        
        if not token_data:
            return None
        
        # Create new access token
        new_access_token = self.create_access_token(
            user_id=token_data.user_id,
            email=token_data.email,
            role=token_data.role
        )
        
        return {
            "access_token": new_access_token,
            "token_type": "bearer"
        }
    
    def create_token_pair(self, user_id: str, email: str, role: str) -> Dict[str, str]:
        """Create both access and refresh tokens"""
        access_token = self.create_access_token(user_id, email, role)
        refresh_token = self.create_refresh_token(user_id, email, role)
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        return self.pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        return self.pwd_context.verify(plain_password, hashed_password)


# Global JWT manager instance
jwt_manager = JWTManager()


def create_access_token(user_id: str, email: str, role: str) -> str:
    """Convenience function to create access token"""
    return jwt_manager.create_access_token(user_id, email, role)


def create_refresh_token(user_id: str, email: str, role: str) -> str:
    """Convenience function to create refresh token"""
    return jwt_manager.create_refresh_token(user_id, email, role)


def verify_token(token: str, token_type: str = "access") -> Optional[TokenData]:
    """Convenience function to verify token"""
    return jwt_manager.verify_token(token, token_type)


def create_token_pair(user_id: str, email: str, role: str) -> Dict[str, str]:
    """Convenience function to create token pair"""
    return jwt_manager.create_token_pair(user_id, email, role)


def hash_password(password: str) -> str:
    """Convenience function to hash password"""
    return jwt_manager.hash_password(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Convenience function to verify password"""
    return jwt_manager.verify_password(plain_password, hashed_password)
