"""
Shared utility functions for all microservices.
"""

import uuid
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import httpx
from fastapi import HTT<PERSON>Ex<PERSON>, status


def generate_id() -> str:
    """Generate a unique ID"""
    return str(uuid.uuid4())


def hash_password(password: str) -> str:
    """Hash a password using SHA-256 (simplified for Sprint 1)"""
    return hashlib.sha256(password.encode()).hexdigest()


def verify_password(password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return hash_password(password) == hashed_password


def generate_session_id() -> str:
    """Generate a session ID"""
    return generate_id()


class InMemoryStore:
    """Simple in-memory store for Sprint 1"""
    
    def __init__(self):
        self._data: Dict[str, Any] = {}
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set a value with optional TTL"""
        expiry = None
        if ttl:
            expiry = datetime.utcnow() + timedelta(seconds=ttl)
        
        self._data[key] = {
            "value": value,
            "expiry": expiry
        }
    
    def get(self, key: str) -> Optional[Any]:
        """Get a value by key"""
        if key not in self._data:
            return None
        
        item = self._data[key]
        
        # Check if expired
        if item["expiry"] and datetime.utcnow() > item["expiry"]:
            del self._data[key]
            return None
        
        return item["value"]
    
    def delete(self, key: str) -> bool:
        """Delete a key"""
        if key in self._data:
            del self._data[key]
            return True
        return False
    
    def exists(self, key: str) -> bool:
        """Check if key exists and is not expired"""
        return self.get(key) is not None
    
    def clear(self) -> None:
        """Clear all data"""
        self._data.clear()


class ServiceClient:
    """HTTP client for inter-service communication"""
    
    def __init__(self, base_url: str, timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
    
    async def get(self, endpoint: str, params: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict[str, Any]:
        """Make a GET request"""
        return await self._request("GET", endpoint, params=params, headers=headers)

    async def post(self, endpoint: str, data: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict[str, Any]:
        """Make a POST request"""
        return await self._request("POST", endpoint, json=data, headers=headers)

    async def put(self, endpoint: str, data: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict[str, Any]:
        """Make a PUT request"""
        return await self._request("PUT", endpoint, json=data, headers=headers)

    async def delete(self, endpoint: str, headers: Optional[Dict] = None) -> Dict[str, Any]:
        """Make a DELETE request"""
        return await self._request("DELETE", endpoint, headers=headers)
    
    async def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make an HTTP request"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.request(method, url, **kwargs)
                response.raise_for_status()
                return response.json()
        
        except httpx.TimeoutException:
            raise HTTPException(
                status_code=status.HTTP_504_GATEWAY_TIMEOUT,
                detail=f"Service timeout: {self.base_url}"
            )
        except httpx.HTTPStatusError as e:
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Service error: {e.response.text}"
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=f"Service unavailable: {str(e)}"
            )


def create_error_response(message: str, error_code: Optional[str] = None, 
                         details: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Create a standardized error response"""
    return {
        "success": False,
        "message": message,
        "error_code": error_code,
        "details": details,
        "timestamp": datetime.utcnow().isoformat()
    }


def create_success_response(data: Any = None, message: str = "Operation completed successfully") -> Dict[str, Any]:
    """Create a standardized success response"""
    response = {
        "success": True,
        "message": message,
        "timestamp": datetime.utcnow().isoformat()
    }
    
    if data is not None:
        response["data"] = data
    
    return response
