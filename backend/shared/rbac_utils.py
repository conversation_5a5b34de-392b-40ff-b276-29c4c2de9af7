"""
Role-Based Access Control (RBAC) Utilities for Medhiq Platform
"""

from typing import Dict, List, Optional, Any
from enum import Enum
from pydantic import BaseModel
from shared.models import UserRole, Permission, RolePermissions, AccessRequest


class ResourceType(str, Enum):
    """Resource types in the system"""
    WORKFLOW = "workflow"
    MARKETPLACE = "marketplace"
    USER = "user"
    SYSTEM = "system"


class ActionType(str, Enum):
    """Action types for RBAC"""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    EXECUTE = "execute"
    PUBLISH = "publish"
    MANAGE = "manage"


class RBACManager:
    """Role-Based Access Control Manager"""
    
    def __init__(self):
        self.role_permissions = self._initialize_role_permissions()
    
    def _initialize_role_permissions(self) -> Dict[UserRole, List[Permission]]:
        """Initialize default role permissions"""
        return {
            UserRole.ADMIN: [
                # Ad<PERSON> has full access to everything
                Permission(resource=ResourceType.WORKFLOW, action=ActionType.READ),
                Permission(resource=ResourceType.WORKFLOW, action=ActionType.WRITE),
                Permission(resource=ResourceType.WORKFLOW, action=ActionType.DELETE),
                Permission(resource=ResourceType.WORKFLOW, action=ActionType.EXECUTE),
                Permission(resource=ResourceType.WORKFLOW, action=ActionType.PUBLISH),
                Permission(resource=ResourceType.WORKFLOW, action=ActionType.MANAGE),
                Permission(resource=ResourceType.MARKETPLACE, action=ActionType.READ),
                Permission(resource=ResourceType.MARKETPLACE, action=ActionType.WRITE),
                Permission(resource=ResourceType.MARKETPLACE, action=ActionType.MANAGE),
                Permission(resource=ResourceType.USER, action=ActionType.READ),
                Permission(resource=ResourceType.USER, action=ActionType.WRITE),
                Permission(resource=ResourceType.USER, action=ActionType.DELETE),
                Permission(resource=ResourceType.USER, action=ActionType.MANAGE),
                Permission(resource=ResourceType.SYSTEM, action=ActionType.MANAGE),
            ],
            
            UserRole.CREATOR: [
                # Creator can manage their own workflows and read marketplace
                Permission(resource=ResourceType.WORKFLOW, action=ActionType.READ),
                Permission(resource=ResourceType.WORKFLOW, action=ActionType.WRITE, 
                          conditions={"owner_only": True}),
                Permission(resource=ResourceType.WORKFLOW, action=ActionType.DELETE, 
                          conditions={"owner_only": True}),
                Permission(resource=ResourceType.WORKFLOW, action=ActionType.PUBLISH, 
                          conditions={"owner_only": True}),
                Permission(resource=ResourceType.MARKETPLACE, action=ActionType.READ),
                Permission(resource=ResourceType.USER, action=ActionType.READ, 
                          conditions={"self_only": True}),
                Permission(resource=ResourceType.USER, action=ActionType.WRITE, 
                          conditions={"self_only": True}),
            ],
            
            UserRole.BUYER: [
                # Buyer can read marketplace and execute workflows
                Permission(resource=ResourceType.WORKFLOW, action=ActionType.READ, 
                          conditions={"published_only": True}),
                Permission(resource=ResourceType.WORKFLOW, action=ActionType.EXECUTE, 
                          conditions={"published_only": True}),
                Permission(resource=ResourceType.MARKETPLACE, action=ActionType.READ),
                Permission(resource=ResourceType.USER, action=ActionType.READ, 
                          conditions={"self_only": True}),
                Permission(resource=ResourceType.USER, action=ActionType.WRITE, 
                          conditions={"self_only": True}),
            ]
        }
    
    def check_permission(
        self, 
        user_role: UserRole, 
        resource: str, 
        action: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Check if user role has permission for resource/action"""
        permissions = self.role_permissions.get(user_role, [])
        
        for permission in permissions:
            if permission.resource == resource and permission.action == action:
                # Check conditions if any
                if permission.conditions:
                    if not self._check_conditions(permission.conditions, context):
                        continue
                return True
        
        return False
    
    def _check_conditions(
        self, 
        conditions: Dict[str, Any], 
        context: Optional[Dict[str, Any]]
    ) -> bool:
        """Check if conditions are met"""
        if not context:
            return False
        
        # Owner-only condition
        if conditions.get("owner_only"):
            resource_owner_id = context.get("resource_owner_id")
            user_id = context.get("user_id")
            if not resource_owner_id or not user_id or resource_owner_id != user_id:
                return False
        
        # Self-only condition (for user resources)
        if conditions.get("self_only"):
            target_user_id = context.get("target_user_id")
            user_id = context.get("user_id")
            if not target_user_id or not user_id or target_user_id != user_id:
                return False
        
        # Published-only condition
        if conditions.get("published_only"):
            is_published = context.get("is_published", False)
            if not is_published:
                return False
        
        return True
    
    def get_user_permissions(self, user_role: UserRole) -> List[Permission]:
        """Get all permissions for a user role"""
        return self.role_permissions.get(user_role, [])
    
    def can_access_workflow(
        self, 
        user_role: UserRole, 
        action: str, 
        workflow_owner_id: str, 
        user_id: str, 
        is_published: bool = False
    ) -> bool:
        """Check if user can access a specific workflow"""
        context = {
            "resource_owner_id": workflow_owner_id,
            "user_id": user_id,
            "is_published": is_published
        }
        
        return self.check_permission(
            user_role=user_role,
            resource=ResourceType.WORKFLOW,
            action=action,
            context=context
        )
    
    def can_manage_user(
        self, 
        user_role: UserRole, 
        action: str, 
        target_user_id: str, 
        user_id: str
    ) -> bool:
        """Check if user can manage another user"""
        context = {
            "target_user_id": target_user_id,
            "user_id": user_id
        }
        
        return self.check_permission(
            user_role=user_role,
            resource=ResourceType.USER,
            action=action,
            context=context
        )
    
    def filter_workflows_by_permission(
        self, 
        workflows: List[Dict[str, Any]], 
        user_role: UserRole, 
        user_id: str
    ) -> List[Dict[str, Any]]:
        """Filter workflows based on user permissions"""
        filtered_workflows = []
        
        for workflow in workflows:
            workflow_owner_id = workflow.get("creator_id")
            is_published = workflow.get("status") == "published"
            
            if self.can_access_workflow(
                user_role=user_role,
                action=ActionType.READ,
                workflow_owner_id=workflow_owner_id,
                user_id=user_id,
                is_published=is_published
            ):
                filtered_workflows.append(workflow)
        
        return filtered_workflows


# Global RBAC manager instance
rbac_manager = RBACManager()


def check_permission(
    user_role: UserRole, 
    resource: str, 
    action: str, 
    context: Optional[Dict[str, Any]] = None
) -> bool:
    """Convenience function to check permission"""
    return rbac_manager.check_permission(user_role, resource, action, context)


def can_access_workflow(
    user_role: UserRole, 
    action: str, 
    workflow_owner_id: str, 
    user_id: str, 
    is_published: bool = False
) -> bool:
    """Convenience function to check workflow access"""
    return rbac_manager.can_access_workflow(
        user_role, action, workflow_owner_id, user_id, is_published
    )


def filter_workflows_by_permission(
    workflows: List[Dict[str, Any]], 
    user_role: UserRole, 
    user_id: str
) -> List[Dict[str, Any]]:
    """Convenience function to filter workflows"""
    return rbac_manager.filter_workflows_by_permission(workflows, user_role, user_id)
