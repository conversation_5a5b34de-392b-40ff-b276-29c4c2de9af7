"""
Shared data models for all microservices.
These models define the common data structures used across services.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, EmailStr


class UserRole(str, Enum):
    """User roles in the system"""
    CREATOR = "creator"
    BUYER = "buyer"
    ADMIN = "admin"


class WorkflowStatus(str, Enum):
    """Workflow status enumeration"""
    DRAFT = "draft"
    PUBLISHED = "published"
    DEPRECATED = "deprecated"
    SUSPENDED = "suspended"


class PermissionLevel(str, Enum):
    """Permission levels for workflow access"""
    OWNER = "owner"
    MANAGER = "manager"
    ANALYST = "analyst"
    VIEWER = "viewer"


# Base Models
class BaseResponse(BaseModel):
    """Base response model for all API responses"""
    success: bool = True
    message: str = "Operation completed successfully"
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ErrorResponse(BaseResponse):
    """Error response model"""
    success: bool = False
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


# User Models
class UserBase(BaseModel):
    """Base user model"""
    email: EmailStr
    name: str
    role: UserRole = UserRole.BUYER


class UserCreate(UserBase):
    """User creation model"""
    password: Optional[str] = None  # Optional for OAuth users


class UserUpdate(BaseModel):
    """User update model"""
    name: Optional[str] = None
    email: Optional[EmailStr] = None


class User(UserBase):
    """Complete user model"""
    user_id: str
    created_at: datetime
    updated_at: datetime
    is_active: bool = True
    
    class Config:
        from_attributes = True


class UserSession(BaseModel):
    """User session model for in-memory storage"""
    user_id: str
    email: str
    name: str
    role: UserRole
    created_at: datetime
    last_accessed: datetime


# Workflow Models
class WorkflowBase(BaseModel):
    """Base workflow model"""
    name: str
    description: str
    category: str
    price: float = Field(ge=0, description="Price in credits")
    tags: List[str] = Field(default_factory=list)


class WorkflowCreate(WorkflowBase):
    """Workflow creation model"""
    workflow_json: Dict[str, Any]  # n8n workflow JSON


class WorkflowUpdate(BaseModel):
    """Workflow update model"""
    name: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    price: Optional[float] = Field(None, ge=0)
    tags: Optional[List[str]] = None
    status: Optional[WorkflowStatus] = None


class Workflow(WorkflowBase):
    """Complete workflow model"""
    workflow_id: str
    creator_id: str
    status: WorkflowStatus = WorkflowStatus.DRAFT
    created_at: datetime
    updated_at: datetime
    execution_count: int = 0
    rating: float = 0.0
    
    class Config:
        from_attributes = True


# Marketplace Models
class AgentFilter(BaseModel):
    """Agent filtering parameters"""
    category: Optional[str] = None
    min_price: Optional[float] = Field(None, ge=0)
    max_price: Optional[float] = Field(None, ge=0)
    tags: Optional[List[str]] = None
    min_rating: Optional[float] = Field(None, ge=0, le=5)


class PaginationParams(BaseModel):
    """Pagination parameters"""
    page: int = Field(1, ge=1, description="Page number")
    size: int = Field(20, ge=1, le=100, description="Items per page")


class PaginatedResponse(BaseModel):
    """Paginated response model"""
    items: List[Any]
    total: int
    page: int
    size: int
    pages: int


# Authentication Models
class LoginRequest(BaseModel):
    """Login request model"""
    email: EmailStr
    password: str


class TokenResponse(BaseResponse):
    """JWT token response model"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int  # seconds


class LoginResponse(TokenResponse):
    """Login response model with user info"""
    user: User


class RefreshTokenRequest(BaseModel):
    """Refresh token request model"""
    refresh_token: str


class OAuthLoginRequest(BaseModel):
    """OAuth login request model"""
    provider: str = Field(..., description="OAuth provider (google, github, etc.)")
    code: str = Field(..., description="Authorization code from OAuth provider")
    state: str = Field(..., description="State parameter for CSRF protection")


class OAuthAuthorizationRequest(BaseModel):
    """OAuth authorization URL request"""
    provider: str = Field(..., description="OAuth provider (google, github, etc.)")
    redirect_uri: Optional[str] = None


class OAuthAuthorizationResponse(BaseResponse):
    """OAuth authorization URL response"""
    auth_url: str
    state: str


class LogoutRequest(BaseModel):
    """Logout request model"""
    token: Optional[str] = None  # For JWT-based logout


# RBAC Models
class Permission(BaseModel):
    """Permission model for RBAC"""
    resource: str  # e.g., "workflow", "marketplace", "user"
    action: str    # e.g., "read", "write", "delete", "execute"
    conditions: Optional[Dict[str, Any]] = None  # Additional conditions


class RolePermissions(BaseModel):
    """Role permissions mapping"""
    role: UserRole
    permissions: List[Permission]


class AccessRequest(BaseModel):
    """Access control request"""
    user_id: str
    resource: str
    action: str
    resource_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None


# Workflow Upload Models
class WorkflowUploadRequest(BaseModel):
    """Workflow upload request model"""
    name: str
    description: str
    category: str
    price: float = Field(ge=0, description="Price in credits")
    tags: List[str] = Field(default_factory=list)
    workflow_json: Dict[str, Any]  # n8n workflow JSON
    is_public: bool = True


class WorkflowUploadResponse(BaseResponse):
    """Workflow upload response model"""
    workflow_id: str
    n8n_workflow_id: Optional[str] = None
    validation_result: Optional[Dict[str, Any]] = None


class WorkflowValidationRequest(BaseModel):
    """Workflow validation request"""
    workflow_json: Dict[str, Any]


class WorkflowValidationResponse(BaseResponse):
    """Workflow validation response"""
    is_valid: bool
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    node_count: int = 0
    connection_count: int = 0


# Health Check Models
class HealthCheck(BaseModel):
    """Health check response model"""
    service: str
    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str = "1.0.0"
    dependencies: Dict[str, str] = Field(default_factory=dict)
