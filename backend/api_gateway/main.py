"""
API Gateway Service - Central entry point for all client requests.
Handles routing, authentication validation, rate limiting, and response transformation.
"""

import sys
import os
from datetime import datetime
from typing import Dict, Any

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from shared.config import APIGatewaySettings
from shared.models import HealthCheck, BaseResponse
from shared.utils import ServiceClient, create_error_response, create_success_response

# Initialize settings
settings = APIGatewaySettings()

# Create FastAPI app
app = FastAPI(
    title="Medhiq - API Gateway",
    description="Central API gateway for the Medhiq AI automation platform",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

# Service clients
auth_client = ServiceClient(settings.auth_service_url)
marketplace_client = ServiceClient(settings.marketplace_service_url)
workflow_client = ServiceClient(settings.workflow_service_url)


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Global exception handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content=create_error_response(
            message=exc.detail,
            error_code=f"HTTP_{exc.status_code}"
        )
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle unexpected exceptions"""
    return JSONResponse(
        status_code=500,
        content=create_error_response(
            message="Internal server error",
            error_code="INTERNAL_ERROR",
            details={"error": str(exc)} if settings.debug else None
        )
    )


@app.get("/health", response_model=HealthCheck)
async def health_check():
    """Health check endpoint"""
    dependencies = {}
    
    # Check auth service
    try:
        await auth_client.get("/health")
        dependencies["auth_service"] = "healthy"
    except Exception:
        dependencies["auth_service"] = "unhealthy"
    
    # Check marketplace service
    try:
        await marketplace_client.get("/health")
        dependencies["marketplace_service"] = "healthy"
    except Exception:
        dependencies["marketplace_service"] = "unhealthy"
    
    # Check workflow service
    try:
        await workflow_client.get("/health")
        dependencies["workflow_service"] = "healthy"
    except Exception:
        dependencies["workflow_service"] = "unhealthy"
    
    return HealthCheck(
        service="api-gateway",
        dependencies=dependencies
    )


# Authentication routes
@app.post(f"{settings.api_prefix}/auth/login")
async def login(request: Request):
    """Proxy login request to auth service"""
    body = await request.json()
    response = await auth_client.post("/auth/login", body)
    return response


@app.post(f"{settings.api_prefix}/auth/logout")
async def logout(request: Request):
    """Proxy logout request to auth service"""
    body = await request.json()
    response = await auth_client.post("/auth/logout", body)
    return response


@app.get(f"{settings.api_prefix}/auth/me")
async def get_current_user(request: Request):
    """Get current user information"""
    # Extract session ID from headers
    session_id = request.headers.get("X-Session-ID")
    if not session_id:
        raise HTTPException(status_code=401, detail="Session ID required")
    
    response = await auth_client.get(f"/auth/me?session_id={session_id}")
    return response


# User management routes
@app.get(f"{settings.api_prefix}/users/me")
async def get_user_profile(request: Request):
    """Get user profile"""
    session_id = request.headers.get("X-Session-ID")
    if not session_id:
        raise HTTPException(status_code=401, detail="Session ID required")
    
    response = await auth_client.get(f"/users/me?session_id={session_id}")
    return response


# Marketplace routes
@app.get(f"{settings.api_prefix}/marketplace/agents")
async def list_agents(request: Request):
    """List marketplace agents"""
    # Forward query parameters
    params = dict(request.query_params)
    response = await marketplace_client.get("/agents", params)
    return response


@app.get(f"{settings.api_prefix}/marketplace/agents/{{agent_id}}")
async def get_agent_details(agent_id: str):
    """Get agent details"""
    response = await marketplace_client.get(f"/agents/{agent_id}")
    return response


@app.get(f"{settings.api_prefix}/marketplace/categories")
async def get_categories():
    """Get marketplace categories"""
    response = await marketplace_client.get("/categories")
    return response


# Workflow routes
@app.get(f"{settings.api_prefix}/workflows/list")
async def list_workflows(request: Request):
    """List workflows"""
    session_id = request.headers.get("X-Session-ID")
    params = dict(request.query_params)
    if session_id:
        params["session_id"] = session_id
    
    response = await workflow_client.get("/workflows", params)
    return response


@app.post(f"{settings.api_prefix}/workflows/upload")
async def upload_workflow(request: Request):
    """Upload a new workflow"""
    session_id = request.headers.get("X-Session-ID")
    if not session_id:
        raise HTTPException(status_code=401, detail="Session ID required")
    
    body = await request.json()
    body["session_id"] = session_id
    response = await workflow_client.post("/workflows", body)
    return response


@app.get("/")
async def root():
    """Root endpoint"""
    return create_success_response(
        data={
            "service": "Medhiq API Gateway",
            "version": "1.0.0",
            "status": "running"
        },
        message="API Gateway is running"
    )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )
