# This Dockerfile should be built from the backend/ directory
# Usage: docker build -f api_gateway/Dockerfile -t api-gateway .

FROM python:3.11-slim

WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy shared modules
COPY shared/ ./shared/

# Copy service code
COPY api_gateway/ ./api_gateway/

# Set Python path to include the app directory
ENV PYTHONPATH=/app

# Expose port
EXPOSE 8000

# Run the application
CMD ["python", "api_gateway/main.py"]
