# This Dockerfile should be built from the backend/ directory
# Usage: docker build -f marketplace_service/Dockerfile -t marketplace-service .

FROM python:3.11-slim

WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy shared modules
COPY shared/ ./shared/

# Copy service code
COPY marketplace_service/ ./marketplace_service/

# Set Python path to include the app directory
ENV PYTHONPATH=/app

# Expose port
EXPOSE 8002

# Run the application
CMD ["python", "marketplace_service/main.py"]
