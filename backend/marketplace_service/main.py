"""
Marketplace Service - Agent listings, search functionality, and categorization.
"""

import sys
import os
from datetime import datetime
from typing import List, Optional

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import FastAPI, Query
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from shared.config import MarketplaceServiceSettings
from shared.models import (
    HealthCheck, Workflow, WorkflowStatus, AgentFilter, 
    PaginationParams, PaginatedResponse
)
from shared.utils import InMemoryStore, generate_id, create_success_response

# Initialize settings
settings = MarketplaceServiceSettings()

# Create FastAPI app
app = FastAPI(
    title="Medhiq - Marketplace Service",
    description="AI agent listings and marketplace functionality for Medhiq platform",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

# In-memory store for Sprint 1
agents_store = InMemoryStore()
categories_store = InMemoryStore()

# Initialize demo data
def initialize_demo_data():
    """Initialize demo marketplace data"""
    # Categories
    categories = [
        {"id": "ecommerce", "name": "E-commerce", "description": "Online retail automation"},
        {"id": "marketing", "name": "Marketing", "description": "Marketing automation and analytics"},
        {"id": "data-processing", "name": "Data Processing", "description": "Data transformation and analysis"},
        {"id": "customer-service", "name": "Customer Service", "description": "Customer support automation"},
        {"id": "finance", "name": "Finance", "description": "Financial data processing"},
        {"id": "healthcare", "name": "Healthcare", "description": "Healthcare workflow automation"}
    ]
    
    for category in categories:
        categories_store.set(f"category:{category['id']}", category)
    
    # Demo agents
    demo_agents = [
        {
            "workflow_id": "agent-1",
            "creator_id": "creator-1",
            "name": "E-commerce Order Processor",
            "description": "Automatically process e-commerce orders, update inventory, and send confirmation emails",
            "category": "ecommerce",
            "price": 5.0,
            "tags": ["orders", "inventory", "email", "automation"],
            "status": WorkflowStatus.PUBLISHED,
            "execution_count": 150,
            "rating": 4.5
        },
        {
            "workflow_id": "agent-2",
            "creator_id": "creator-1",
            "name": "Social Media Analytics",
            "description": "Collect and analyze social media metrics across multiple platforms",
            "category": "marketing",
            "price": 8.0,
            "tags": ["social-media", "analytics", "reporting", "metrics"],
            "status": WorkflowStatus.PUBLISHED,
            "execution_count": 89,
            "rating": 4.2
        },
        {
            "workflow_id": "agent-3",
            "creator_id": "creator-1",
            "name": "Customer Support Ticket Router",
            "description": "Automatically categorize and route customer support tickets to appropriate teams",
            "category": "customer-service",
            "price": 3.0,
            "tags": ["support", "tickets", "routing", "classification"],
            "status": WorkflowStatus.PUBLISHED,
            "execution_count": 234,
            "rating": 4.7
        },
        {
            "workflow_id": "agent-4",
            "creator_id": "creator-1",
            "name": "Financial Report Generator",
            "description": "Generate comprehensive financial reports from multiple data sources",
            "category": "finance",
            "price": 12.0,
            "tags": ["finance", "reporting", "data-analysis", "automation"],
            "status": WorkflowStatus.PUBLISHED,
            "execution_count": 67,
            "rating": 4.8
        },
        {
            "workflow_id": "agent-5",
            "creator_id": "creator-1",
            "name": "Data Cleaner Pro",
            "description": "Clean and standardize data from CSV files and databases",
            "category": "data-processing",
            "price": 6.0,
            "tags": ["data-cleaning", "csv", "database", "standardization"],
            "status": WorkflowStatus.PUBLISHED,
            "execution_count": 112,
            "rating": 4.3
        }
    ]
    
    for agent_data in demo_agents:
        agent = Workflow(
            **agent_data,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        agents_store.set(f"agent:{agent.workflow_id}", agent.dict())

# Initialize demo data on startup
initialize_demo_data()


@app.get("/health", response_model=HealthCheck)
async def health_check():
    """Health check endpoint"""
    return HealthCheck(service="marketplace-service")


@app.get("/agents")
async def list_agents(
    category: Optional[str] = Query(None, description="Filter by category"),
    min_price: Optional[float] = Query(None, ge=0, description="Minimum price"),
    max_price: Optional[float] = Query(None, ge=0, description="Maximum price"),
    tags: Optional[str] = Query(None, description="Comma-separated tags"),
    min_rating: Optional[float] = Query(None, ge=0, le=5, description="Minimum rating"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search query")
):
    """List marketplace agents with filtering and pagination"""
    
    # Get all agents
    all_agents = []
    for key in agents_store._data.keys():
        if key.startswith("agent:"):
            agent_data = agents_store.get(key)
            if agent_data:
                all_agents.append(Workflow(**agent_data))
    
    # Filter agents
    filtered_agents = []
    for agent in all_agents:
        # Only show published agents
        if agent.status != WorkflowStatus.PUBLISHED:
            continue
            
        # Category filter
        if category and agent.category != category:
            continue
            
        # Price filters
        if min_price is not None and agent.price < min_price:
            continue
        if max_price is not None and agent.price > max_price:
            continue
            
        # Rating filter
        if min_rating is not None and agent.rating < min_rating:
            continue
            
        # Tags filter
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",")]
            if not any(tag in agent.tags for tag in tag_list):
                continue
        
        # Search filter (simple text search)
        if search:
            search_lower = search.lower()
            if not (search_lower in agent.name.lower() or 
                   search_lower in agent.description.lower() or
                   any(search_lower in tag.lower() for tag in agent.tags)):
                continue
        
        filtered_agents.append(agent)
    
    # Sort by rating (descending) then by execution count
    filtered_agents.sort(key=lambda x: (-x.rating, -x.execution_count))
    
    # Pagination
    total = len(filtered_agents)
    start_idx = (page - 1) * size
    end_idx = start_idx + size
    paginated_agents = filtered_agents[start_idx:end_idx]
    
    return PaginatedResponse(
        items=[agent.dict() for agent in paginated_agents],
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


@app.get("/agents/{agent_id}")
async def get_agent_details(agent_id: str):
    """Get detailed information about a specific agent"""
    agent_data = agents_store.get(f"agent:{agent_id}")
    if not agent_data:
        return create_error_response("Agent not found", "AGENT_NOT_FOUND")
    
    agent = Workflow(**agent_data)
    return create_success_response(agent.dict())


@app.get("/categories")
async def get_categories():
    """Get all marketplace categories"""
    categories = []
    for key in categories_store._data.keys():
        if key.startswith("category:"):
            category_data = categories_store.get(key)
            if category_data:
                categories.append(category_data)
    
    # Sort categories by name
    categories.sort(key=lambda x: x["name"])
    
    return create_success_response(categories)


@app.get("/search")
async def search_agents(
    q: str = Query(..., description="Search query"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Items per page")
):
    """Full-text search across agents"""
    # For Sprint 1, use simple text matching
    # In future sprints, this will use PostgreSQL full-text search
    
    search_lower = q.lower()
    matching_agents = []
    
    for key in agents_store._data.keys():
        if key.startswith("agent:"):
            agent_data = agents_store.get(key)
            if agent_data:
                agent = Workflow(**agent_data)
                
                # Only search published agents
                if agent.status != WorkflowStatus.PUBLISHED:
                    continue
                
                # Calculate relevance score
                score = 0
                if search_lower in agent.name.lower():
                    score += 10
                if search_lower in agent.description.lower():
                    score += 5
                if any(search_lower in tag.lower() for tag in agent.tags):
                    score += 3
                if search_lower in agent.category.lower():
                    score += 2
                
                if score > 0:
                    matching_agents.append((agent, score))
    
    # Sort by relevance score (descending)
    matching_agents.sort(key=lambda x: -x[1])
    agents_only = [agent for agent, score in matching_agents]
    
    # Pagination
    total = len(agents_only)
    start_idx = (page - 1) * size
    end_idx = start_idx + size
    paginated_agents = agents_only[start_idx:end_idx]
    
    return PaginatedResponse(
        items=[agent.dict() for agent in paginated_agents],
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )
