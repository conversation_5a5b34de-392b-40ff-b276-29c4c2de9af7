# Environment Configuration
ENVIRONMENT=development
DEBUG=true

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# JWT Security Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Service URLs
AUTH_SERVICE_URL=http://localhost:8001
MARKETPLACE_SERVICE_URL=http://localhost:8002
WORKFLOW_SERVICE_URL=http://localhost:8003
PAYMENT_SERVICE_URL=http://localhost:8004
EXECUTION_SERVICE_URL=http://localhost:8005
NOTIFICATION_SERVICE_URL=http://localhost:8006

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000

# Database Configuration (for future sprints)
# DATABASE_URL=postgresql://user:password@localhost:5432/ai_marketplace
# REDIS_URL=redis://localhost:6379

# n8n Integration (Sprint 2)
N8N_BASE_URL=http://localhost:5678
N8N_API_KEY=your-n8n-api-key
N8N_WEBHOOK_URL=http://localhost:5678/webhook

# OAuth Configuration (Sprint 2)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
OAUTH_REDIRECT_URI=http://localhost:3000/auth/callback

# External Services (for future sprints)
# STRIPE_API_KEY=sk_test_your_stripe_key
# STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
# GITHUB_CLIENT_ID=your-github-client-id
# GITHUB_CLIENT_SECRET=your-github-client-secret
