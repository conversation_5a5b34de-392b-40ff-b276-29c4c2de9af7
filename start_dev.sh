#!/bin/bash

# Medhiq - Development Startup Script

echo "🚀 Starting Medhiq Development Environment"
echo "=========================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. You can modify it if needed."
fi

# Build and start services
echo "🔨 Building and starting services..."
docker-compose up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check service health
echo "🔍 Checking service health..."
python3 test_api.py

echo ""
echo "🎉 Medhiq development environment is ready!"
echo ""
echo "📋 Available Services:"
echo "   • Medhiq Frontend:    http://localhost:3000"
echo "   • API Gateway:        http://localhost:8000"
echo "   • Auth Service:       http://localhost:8001"
echo "   • Marketplace Service: http://localhost:8002"
echo "   • Workflow Service:   http://localhost:8003"
echo ""
echo "👤 Demo Credentials:"
echo "   • Creator: <EMAIL> / password123"
echo "   • Buyer:   <EMAIL> / password123"
echo ""
echo "📚 API Documentation: http://localhost:8000/docs"
echo ""
echo "🛑 To stop services: docker-compose down"
echo "📊 To view logs: docker-compose logs -f [service-name]"
