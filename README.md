# Medhiq

A microservices-based AI automation platform connecting workflow creators with business users through a credit-based marketplace.

## Project Structure

```
medhiq/
├── backend/                 # Backend microservices
│   ├── api_gateway/        # Central API gateway
│   ├── auth_service/       # Authentication service
│   ├── marketplace_service/ # Marketplace functionality
│   ├── payment_service/    # Payment and credit management
│   ├── workflow_service/   # n8n workflow management
│   ├── execution_service/  # Workflow execution monitoring
│   ├── notification_service/ # Notifications and alerts
│   └── shared/            # Shared utilities and models
├── frontend/              # Next.js frontend application
├── docs/                  # Documentation
└── docker-compose.yml     # Development environment
```

## Sprint 1: Basic Web Application (MVP Foundation)

### Current Implementation Status
- ✅ Project structure setup
- ✅ Basic FastAPI backend with in-memory session management
- ✅ Next.js frontend with TypeScript
- ✅ Basic pages: Home, Login, Dashboard, Marketplace
- ✅ Responsive design with Tailwind CSS
- ✅ Basic REST endpoints and validation
- ✅ Microservices architecture (API Gateway, Auth, Marketplace, Workflow)
- ✅ Docker containerization with Docker Compose
- ✅ Demo data and user accounts
- ✅ Authentication flow with session management
- ✅ Marketplace browsing with search and filtering
- ✅ Basic workflow management for creators
- ✅ Unit tests for core functionality

### Getting Started

#### Prerequisites
- Python 3.11+
- Node.js 18+
- Docker and Docker Compose

#### Quick Start

1. **Using the startup script (Recommended)**:
```bash
./start_dev.sh
```

2. **Manual Docker Compose setup**:
```bash
cp .env.example .env
docker-compose up --build
```

3. **Manual development setup**:

   **Backend**:
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt

   # Start each service in separate terminals
   python -m api_gateway.main
   python -m auth_service.main
   python -m marketplace_service.main
   python -m workflow_service.main
   ```

   **Frontend**:
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

#### Demo Credentials

- **Creator Account**: <EMAIL> / password123
- **Buyer Account**: <EMAIL> / password123

#### API Endpoints

**Authentication**:
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `GET /api/v1/auth/me` - Current user information

**Marketplace**:
- `GET /api/v1/marketplace/agents` - List agents with filtering
- `GET /api/v1/marketplace/agents/{id}` - Get agent details
- `GET /api/v1/marketplace/categories` - Get categories
- `GET /api/v1/marketplace/search` - Search agents

**Workflows**:
- `GET /api/v1/workflows/list` - List user workflows
- `POST /api/v1/workflows/upload` - Upload new workflow
- `GET /api/v1/workflows/{id}` - Get workflow details
- `PUT /api/v1/workflows/{id}` - Update workflow
- `POST /api/v1/workflows/{id}/publish` - Publish workflow

**Health Checks**:
- `GET /health` - API Gateway health
- Individual service health endpoints on ports 8001-8003

#### Frontend Pages

- `/` - Home page with feature overview
- `/login` - Login page with demo credentials
- `/dashboard` - User dashboard (role-specific)
- `/marketplace` - Marketplace browsing with search/filter
- `/marketplace/{id}` - Agent detail pages (planned)
- `/upload` - Workflow upload for creators (planned)

### Technology Stack

#### Backend
- **FastAPI** - High-performance async API framework
- **Pydantic** - Data validation and serialization
- **Uvicorn** - ASGI server

#### Frontend
- **Next.js 14** - React framework with SSR
- **TypeScript** - Type safety
- **Tailwind CSS** - Utility-first CSS framework
- **React Context** - State management

### Testing

#### Running Tests
```bash
# Backend unit tests
cd backend
python -m pytest tests/ -v

# API integration tests
python test_api.py
```

#### Manual Testing
1. Start the development environment: `./start_dev.sh`
2. Open http://localhost:3000 in your browser
3. Login with demo credentials
4. Test the user flows:
   - **Creator**: Login → Dashboard → Upload workflow (planned)
   - **Buyer**: Login → Marketplace → Browse agents → Dashboard

### Troubleshooting

#### Common Issues

**Services not starting**:
- Ensure Docker is running
- Check port availability (3000, 8000-8003)
- Run `docker-compose logs [service-name]` to check logs

**Frontend not connecting to backend**:
- Verify API_URL in frontend/.env.local
- Check CORS settings in backend configuration
- Ensure API Gateway is running on port 8000

**Authentication issues**:
- Clear browser localStorage
- Check session expiration (24 hours)
- Verify demo credentials are correct

#### Development Commands

```bash
# View service logs
docker-compose logs -f api-gateway
docker-compose logs -f frontend

# Restart specific service
docker-compose restart auth-service

# Rebuild and restart all services
docker-compose down && docker-compose up --build

# Run backend tests
cd backend && python -m pytest tests/

# Check service health
curl http://localhost:8000/health
```

### Next Steps (Sprint 2)
- Replace in-memory sessions with JWT tokens
- Implement OAuth integration (Google)
- Set up n8n instance connection
- Add workflow upload functionality
- Implement basic RBAC
