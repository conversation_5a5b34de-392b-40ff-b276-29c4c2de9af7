// Authentication hook using React Context with JWT tokens
import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, LoginRequest } from '@/types';
import { authManager } from '@/lib/auth';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  getOAuthUrl: (provider: string) => Promise<string>;
  oauthLogin: (provider: string, code: string, state: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  const login = async (credentials: LoginRequest) => {
    try {
      const response = await authManager.login(credentials.email, credentials.password);
      if (response.user) {
        setUser(response.user);
      } else {
        throw new Error('Login failed - no user data received');
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      // Call logout endpoint (optional for JWT)
      await fetch('/api/v1/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...authManager.getAuthHeader(),
        },
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      authManager.logout();
      setUser(null);
    }
  };

  const refreshUser = async () => {
    try {
      if (authManager.isAuthenticated()) {
        const response = await authManager.apiRequest('/api/v1/auth/me');
        if (response.ok) {
          const userData = await response.json();
          setUser(userData);
        } else {
          throw new Error('Failed to get user data');
        }
      }
    } catch (error) {
      console.error('Failed to refresh user:', error);
      authManager.logout();
      setUser(null);
    }
  };

  const getOAuthUrl = async (provider: string): Promise<string> => {
    try {
      const response = await authManager.getOAuthUrl(provider);
      return response.auth_url;
    } catch (error) {
      console.error('Failed to get OAuth URL:', error);
      throw error;
    }
  };

  const oauthLogin = async (provider: string, code: string, state: string) => {
    try {
      const response = await authManager.oauthLogin(provider, code, state);
      if (response.user) {
        setUser(response.user);
      } else {
        throw new Error('OAuth login failed - no user data received');
      }
    } catch (error) {
      console.error('OAuth login error:', error);
      throw error;
    }
  };

  useEffect(() => {
    const initAuth = async () => {
      setIsLoading(true);
      try {
        // Check if user is stored locally and token exists
        const storedUser = authManager.getUser();
        if (storedUser && authManager.isAuthenticated()) {
          setUser(storedUser);
          // Optionally refresh user data from server
          await refreshUser();
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        authManager.logout();
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const value = {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    refreshUser,
    getOAuthUrl,
    oauthLogin,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}