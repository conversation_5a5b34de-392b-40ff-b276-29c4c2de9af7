'use client';

import { useAuth } from '@/hooks/useAuth';
import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';
import { Workflow } from '@/types';
import { Plus, Eye, Edit, BarChart3, DollarSign, Users, TrendingUp } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export default function DashboardPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();

  // Redirect if not authenticated
  if (!authLoading && !isAuthenticated) {
    router.push('/login');
    return null;
  }

  // Fetch user's workflows
  const { data: workflowsResponse, isLoading } = useQuery(
    'user-workflows',
    () => apiClient.getWorkflows(),
    {
      enabled: isAuthenticated,
    }
  );

  const workflows = workflowsResponse?.data || [];

  if (authLoading || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  const isCreator = user?.role === 'creator';

  // Calculate stats for creators
  const stats = isCreator ? {
    totalWorkflows: workflows.length,
    publishedWorkflows: workflows.filter((w: Workflow) => w.status === 'published').length,
    totalExecutions: workflows.reduce((sum: number, w: Workflow) => sum + w.execution_count, 0),
    averageRating: workflows.length > 0 
      ? workflows.reduce((sum: number, w: Workflow) => sum + w.rating, 0) / workflows.length 
      : 0,
  } : null;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900">
            <span className="bg-gradient-to-r from-primary-600 to-primary-700 bg-clip-text text-transparent">
              Medhiq
            </span>{' '}
            {isCreator ? 'Creator Dashboard' : 'Dashboard'}
          </h1>
          <p className="mt-3 text-xl text-gray-600">
            {isCreator
              ? 'Manage your workflows and track performance across the platform'
              : 'Monitor your deployed agents and track usage analytics'
            }
          </p>
        </div>

        {/* Stats Cards for Creators */}
        {isCreator && stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <BarChart3 className="h-8 w-8 text-primary-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Workflows</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.totalWorkflows}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Eye className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Published</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.publishedWorkflows}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Executions</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.totalExecutions}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrendingUp className="h-8 w-8 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Avg. Rating</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {stats.averageRating.toFixed(1)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Workflows Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">
                {isCreator ? 'My Workflows' : 'Deployed Agents'}
              </h2>
              {isCreator && (
                <Link
                  href="/upload"
                  className="btn-primary"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Upload Workflow
                </Link>
              )}
            </div>
          </div>

          <div className="p-6">
            {workflows.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  {isCreator ? (
                    <Plus className="w-8 h-8 text-gray-400" />
                  ) : (
                    <BarChart3 className="w-8 h-8 text-gray-400" />
                  )}
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {isCreator ? 'No workflows yet' : 'No agents deployed'}
                </h3>
                <p className="text-gray-600 mb-4">
                  {isCreator 
                    ? 'Upload your first n8n workflow to get started'
                    : 'Browse the marketplace to deploy your first AI agent'
                  }
                </p>
                {isCreator ? (
                  <Link href="/upload" className="btn-primary">
                    Upload Workflow
                  </Link>
                ) : (
                  <Link href="/marketplace" className="btn-primary">
                    Browse Marketplace
                  </Link>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {workflows.map((workflow: Workflow) => (
                  <div
                    key={workflow.workflow_id}
                    className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {workflow.name}
                      </h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        workflow.status === 'published' 
                          ? 'bg-green-100 text-green-800'
                          : workflow.status === 'draft'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {workflow.status}
                      </span>
                    </div>

                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                      {workflow.description}
                    </p>

                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <DollarSign className="w-4 h-4 mr-1" />
                          {workflow.price} credits
                        </div>
                        <div className="flex items-center">
                          <Users className="w-4 h-4 mr-1" />
                          {workflow.execution_count} runs
                        </div>
                        <div className="flex items-center">
                          <TrendingUp className="w-4 h-4 mr-1" />
                          {workflow.rating.toFixed(1)} rating
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {workflow.tags.slice(0, 3).map((tag) => (
                          <span
                            key={tag}
                            className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-600"
                          >
                            {tag}
                          </span>
                        ))}
                        {workflow.tags.length > 3 && (
                          <span className="text-xs text-gray-500">
                            +{workflow.tags.length - 3} more
                          </span>
                        )}
                      </div>

                      <div className="flex items-center space-x-2">
                        <Link
                          href={`/workflow/${workflow.workflow_id}`}
                          className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                        >
                          View Details
                        </Link>
                        {isCreator && (
                          <Link
                            href={`/workflow/${workflow.workflow_id}/edit`}
                            className="text-gray-600 hover:text-gray-700"
                          >
                            <Edit className="w-4 h-4" />
                          </Link>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions for Buyers */}
        {!isCreator && (
          <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link
                href="/marketplace"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <BarChart3 className="w-8 h-8 text-primary-600 mr-3" />
                <div>
                  <h3 className="font-medium text-gray-900">Browse Marketplace</h3>
                  <p className="text-sm text-gray-600">Find new AI agents</p>
                </div>
              </Link>

              <Link
                href="/credits"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <DollarSign className="w-8 h-8 text-green-600 mr-3" />
                <div>
                  <h3 className="font-medium text-gray-900">Buy Credits</h3>
                  <p className="text-sm text-gray-600">Add funds to your account</p>
                </div>
              </Link>

              <Link
                href="/team"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Users className="w-8 h-8 text-blue-600 mr-3" />
                <div>
                  <h3 className="font-medium text-gray-900">Manage Team</h3>
                  <p className="text-sm text-gray-600">Invite team members</p>
                </div>
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
