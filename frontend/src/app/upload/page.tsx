'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { Upload, FileText, AlertCircle, CheckCircle, X } from 'lucide-react';

interface WorkflowValidation {
  is_valid: boolean;
  errors: string[];
  warnings: string[];
  node_count: number;
  connection_count: number;
}

export default function UploadPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    price: 0,
    tags: '',
    is_public: true,
  });
  
  const [workflowFile, setWorkflowFile] = useState<File | null>(null);
  const [workflowJson, setWorkflowJson] = useState<any>(null);
  const [validation, setValidation] = useState<WorkflowValidation | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Redirect if not authenticated or not a creator
  if (!isAuthenticated || user?.role !== 'creator') {
    router.push('/login');
    return null;
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith('.json')) {
      setError('Please select a JSON file');
      return;
    }

    setWorkflowFile(file);
    setError('');
    setValidation(null);

    try {
      const text = await file.text();
      const json = JSON.parse(text);
      setWorkflowJson(json);
      
      // Auto-populate name if available
      if (json.name && !formData.name) {
        setFormData(prev => ({ ...prev, name: json.name }));
      }
    } catch (err) {
      setError('Invalid JSON file');
      setWorkflowFile(null);
      setWorkflowJson(null);
    }
  };

  const validateWorkflow = async () => {
    if (!workflowJson) return;

    setIsValidating(true);
    setError('');

    try {
      const response = await fetch('/api/v1/workflows/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ workflow_json: workflowJson }),
      });

      if (!response.ok) {
        throw new Error('Validation failed');
      }

      const result = await response.json();
      setValidation(result);
    } catch (err: any) {
      setError(err.message || 'Validation failed');
    } finally {
      setIsValidating(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!workflowJson) {
      setError('Please select a workflow file');
      return;
    }

    if (validation && !validation.is_valid) {
      setError('Please fix validation errors before uploading');
      return;
    }

    setIsUploading(true);
    setError('');

    try {
      const response = await fetch('/api/v1/workflows/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('medhiq_access_token')}`,
        },
        body: JSON.stringify({
          ...formData,
          tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
          workflow_json: workflowJson,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Upload failed');
      }

      const result = await response.json();
      setSuccess('Workflow uploaded successfully!');
      
      // Redirect to dashboard after a delay
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
      
    } catch (err: any) {
      setError(err.message || 'Upload failed');
    } finally {
      setIsUploading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Upload Workflow
            </h1>
            <p className="text-gray-600">
              Upload your n8n workflow to the Medhiq marketplace
            </p>
          </div>

          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-xl p-4">
              <div className="flex">
                <AlertCircle className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          {success && (
            <div className="mb-6 bg-green-50 border border-green-200 rounded-xl p-4">
              <div className="flex">
                <CheckCircle className="h-5 w-5 text-green-400" />
                <div className="ml-3">
                  <p className="text-sm text-green-800">{success}</p>
                </div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* File Upload */}
            <div>
              <label className="label">
                Workflow File (JSON)
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-xl hover:border-primary-400 transition-colors">
                <div className="space-y-1 text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="flex text-sm text-gray-600">
                    <label
                      htmlFor="file-upload"
                      className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"
                    >
                      <span>Upload a file</span>
                      <input
                        id="file-upload"
                        name="file-upload"
                        type="file"
                        accept=".json"
                        className="sr-only"
                        onChange={handleFileChange}
                      />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                  </div>
                  <p className="text-xs text-gray-500">
                    JSON files only
                  </p>
                </div>
              </div>
              
              {workflowFile && (
                <div className="mt-2 flex items-center text-sm text-gray-600">
                  <FileText className="h-4 w-4 mr-2" />
                  {workflowFile.name}
                  <button
                    type="button"
                    onClick={() => {
                      setWorkflowFile(null);
                      setWorkflowJson(null);
                      setValidation(null);
                    }}
                    className="ml-2 text-red-500 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              )}
            </div>

            {/* Validation */}
            {workflowJson && (
              <div>
                <button
                  type="button"
                  onClick={validateWorkflow}
                  disabled={isValidating}
                  className="btn btn-outline"
                >
                  {isValidating ? 'Validating...' : 'Validate Workflow'}
                </button>
                
                {validation && (
                  <div className="mt-4 p-4 border rounded-xl">
                    <div className="flex items-center mb-2">
                      {validation.is_valid ? (
                        <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      ) : (
                        <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                      )}
                      <span className={`font-medium ${validation.is_valid ? 'text-green-800' : 'text-red-800'}`}>
                        {validation.is_valid ? 'Workflow is valid' : 'Workflow has errors'}
                      </span>
                    </div>
                    
                    <div className="text-sm text-gray-600 mb-2">
                      Nodes: {validation.node_count} | Connections: {validation.connection_count}
                    </div>
                    
                    {validation.errors.length > 0 && (
                      <div className="mb-2">
                        <p className="text-sm font-medium text-red-800">Errors:</p>
                        <ul className="text-sm text-red-700 list-disc list-inside">
                          {validation.errors.map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    {validation.warnings.length > 0 && (
                      <div>
                        <p className="text-sm font-medium text-yellow-800">Warnings:</p>
                        <ul className="text-sm text-yellow-700 list-disc list-inside">
                          {validation.warnings.map((warning, index) => (
                            <li key={index}>{warning}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Workflow Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="name" className="label">
                  Workflow Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  className="input"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Enter workflow name"
                />
              </div>

              <div>
                <label htmlFor="category" className="label">
                  Category
                </label>
                <select
                  id="category"
                  name="category"
                  required
                  className="input"
                  value={formData.category}
                  onChange={handleChange}
                >
                  <option value="">Select a category</option>
                  <option value="automation">Automation</option>
                  <option value="data-processing">Data Processing</option>
                  <option value="integration">Integration</option>
                  <option value="ai-ml">AI & Machine Learning</option>
                  <option value="communication">Communication</option>
                  <option value="productivity">Productivity</option>
                </select>
              </div>
            </div>

            <div>
              <label htmlFor="description" className="label">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                rows={4}
                required
                className="input"
                value={formData.description}
                onChange={handleChange}
                placeholder="Describe what your workflow does..."
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="price" className="label">
                  Price (Credits)
                </label>
                <input
                  type="number"
                  id="price"
                  name="price"
                  min="0"
                  step="0.01"
                  required
                  className="input"
                  value={formData.price}
                  onChange={handleChange}
                />
              </div>

              <div>
                <label htmlFor="tags" className="label">
                  Tags (comma-separated)
                </label>
                <input
                  type="text"
                  id="tags"
                  name="tags"
                  className="input"
                  value={formData.tags}
                  onChange={handleChange}
                  placeholder="automation, api, webhook"
                />
              </div>
            </div>

            <div className="flex items-center">
              <input
                id="is_public"
                name="is_public"
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                checked={formData.is_public}
                onChange={handleChange}
              />
              <label htmlFor="is_public" className="ml-2 block text-sm text-gray-900">
                Make this workflow public in the marketplace
              </label>
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => router.push('/dashboard')}
                className="btn btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isUploading || !workflowJson || (validation && !validation.is_valid)}
                className="btn btn-primary"
              >
                {isUploading ? 'Uploading...' : 'Upload Workflow'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
