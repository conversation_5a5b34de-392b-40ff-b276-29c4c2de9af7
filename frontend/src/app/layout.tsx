import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { AuthProvider } from '@/hooks/useAuth';
import { QueryClient, QueryClientProvider } from 'react-query';
import { useState } from 'react';
import ClientLayout from '@/components/ClientLayout';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Medhiq - AI Automation Platform',
  description: 'Connect AI workflow creators with business users through a credit-based marketplace',
  keywords: 'AI, automation, workflows, n8n, marketplace, business, medhiq',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ClientLayout>
          {children}
        </ClientLayout>
      </body>
    </html>
  );
}
