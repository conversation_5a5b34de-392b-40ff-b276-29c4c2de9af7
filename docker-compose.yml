version: '3.8'

services:
  # API Gateway Service
  api-gateway:
    build:
      context: ./backend/api_gateway
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - AUTH_SERVICE_URL=http://auth-service:8001
      - MARKETPLACE_SERVICE_URL=http://marketplace-service:8002
      - WORKFLOW_SERVICE_URL=http://workflow-service:8003
    volumes:
      - ./backend:/app
    depends_on:
      - auth-service
      - marketplace-service
      - workflow-service
    networks:
      - app-network

  # Authentication Service
  auth-service:
    build:
      context: ./backend
      dockerfile: auth_service/Dockerfile
    ports:
      - "8001:8001"
    environment:
      - ENVIRONMENT=development
      - API_HOST=0.0.0.0
      - API_PORT=8001
    volumes:
      - ./backend:/app
    networks:
      - app-network

  # Marketplace Service
  marketplace-service:
    build:
      context: ./backend
      dockerfile: marketplace_service/Dockerfile
    ports:
      - "8002:8002"
    environment:
      - ENVIRONMENT=development
      - API_HOST=0.0.0.0
      - API_PORT=8002
    volumes:
      - ./backend:/app
    networks:
      - app-network

  # Workflow Service
  workflow-service:
    build:
      context: ./backend
      dockerfile: workflow_service/Dockerfile
    ports:
      - "8003:8003"
    environment:
      - ENVIRONMENT=development
      - API_HOST=0.0.0.0
      - API_PORT=8003
    volumes:
      - ./backend:/app
    networks:
      - app-network

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - api-gateway
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  node_modules:
