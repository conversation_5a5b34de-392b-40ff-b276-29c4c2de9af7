#!/usr/bin/env python3
"""
Test script for Sprint 2: JWT Authentication & OAuth Integration
"""

import requests
import json
import time
from typing import Dict, Any

# API Base URL
BASE_URL = "http://localhost:8000/api/v1"

def test_jwt_login():
    """Test JWT-based login"""
    print("🔐 Testing JWT Login...")
    
    # Test login with demo credentials
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    print(f"Login Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Login successful!")
        print(f"   User: {data.get('user', {}).get('name')}")
        print(f"   Role: {data.get('user', {}).get('role')}")
        print(f"   Token Type: {data.get('token_type')}")
        print(f"   Expires In: {data.get('expires_in')} seconds")
        return data.get('access_token'), data.get('refresh_token')
    else:
        print(f"❌ Login failed: {response.text}")
        return None, None

def test_jwt_auth(access_token: str):
    """Test JWT authentication"""
    print("\n🔑 Testing JWT Authentication...")
    
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    
    # Test getting current user
    response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
    print(f"Auth Status: {response.status_code}")
    
    if response.status_code == 200:
        user_data = response.json()
        print(f"✅ Authentication successful!")
        print(f"   User ID: {user_data.get('user_id')}")
        print(f"   Email: {user_data.get('email')}")
        print(f"   Role: {user_data.get('role')}")
    else:
        print(f"❌ Authentication failed: {response.text}")

def test_refresh_token(refresh_token: str):
    """Test token refresh"""
    print("\n🔄 Testing Token Refresh...")
    
    refresh_data = {
        "refresh_token": refresh_token
    }
    
    response = requests.post(f"{BASE_URL}/auth/refresh", json=refresh_data)
    print(f"Refresh Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Token refresh successful!")
        print(f"   New Token Type: {data.get('token_type')}")
        print(f"   Expires In: {data.get('expires_in')} seconds")
        return data.get('access_token')
    else:
        print(f"❌ Token refresh failed: {response.text}")
        return None

def test_oauth_authorization():
    """Test OAuth authorization URL generation"""
    print("\n🌐 Testing OAuth Authorization...")
    
    oauth_data = {
        "provider": "google"
    }
    
    response = requests.post(f"{BASE_URL}/auth/oauth/authorize", json=oauth_data)
    print(f"OAuth Auth Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ OAuth authorization URL generated!")
        print(f"   Provider: google")
        print(f"   State: {data.get('state')[:20]}...")
        print(f"   Auth URL: {data.get('auth_url')[:80]}...")
    else:
        print(f"❌ OAuth authorization failed: {response.text}")

def test_workflow_validation():
    """Test workflow validation endpoint"""
    print("\n📋 Testing Workflow Validation...")
    
    # Sample n8n workflow JSON
    sample_workflow = {
        "name": "Test Workflow",
        "nodes": [
            {
                "name": "Start",
                "type": "n8n-nodes-base.start",
                "position": [240, 300],
                "parameters": {}
            },
            {
                "name": "HTTP Request",
                "type": "n8n-nodes-base.httpRequest",
                "position": [460, 300],
                "parameters": {
                    "url": "https://api.example.com/data",
                    "method": "GET"
                }
            }
        ],
        "connections": {
            "Start": {
                "main": [
                    [
                        {
                            "node": "HTTP Request",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            }
        }
    }
    
    validation_data = {
        "workflow_json": sample_workflow
    }
    
    response = requests.post(f"{BASE_URL}/workflows/validate", json=validation_data)
    print(f"Validation Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Workflow validation successful!")
        print(f"   Valid: {data.get('is_valid')}")
        print(f"   Node Count: {data.get('node_count')}")
        print(f"   Connection Count: {data.get('connection_count')}")
        if data.get('errors'):
            print(f"   Errors: {data.get('errors')}")
        if data.get('warnings'):
            print(f"   Warnings: {data.get('warnings')}")
    else:
        print(f"❌ Workflow validation failed: {response.text}")

def test_workflow_upload(access_token: str):
    """Test workflow upload with JWT authentication"""
    print("\n📤 Testing Workflow Upload...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # Sample workflow upload data
    upload_data = {
        "name": "JWT Test Workflow",
        "description": "A test workflow uploaded via JWT authentication",
        "category": "automation",
        "price": 5.0,
        "tags": ["test", "jwt", "automation"],
        "is_public": True,
        "workflow_json": {
            "name": "JWT Test Workflow",
            "nodes": [
                {
                    "name": "Start",
                    "type": "n8n-nodes-base.start",
                    "position": [240, 300],
                    "parameters": {}
                }
            ],
            "connections": {}
        }
    }
    
    response = requests.post(f"{BASE_URL}/workflows/upload", json=upload_data, headers=headers)
    print(f"Upload Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Workflow upload successful!")
        print(f"   Workflow ID: {data.get('workflow_id')}")
        print(f"   n8n Workflow ID: {data.get('n8n_workflow_id', 'N/A (n8n not connected)')}")
    else:
        print(f"❌ Workflow upload failed: {response.text}")

def test_health_checks():
    """Test health checks for all services"""
    print("\n🏥 Testing Service Health Checks...")
    
    services = [
        ("API Gateway", f"{BASE_URL.replace('/api/v1', '')}/health"),
        ("Auth Service", "http://localhost:8001/health"),
        ("Marketplace Service", "http://localhost:8002/health"),
        ("Workflow Service", "http://localhost:8003/health")
    ]
    
    for service_name, health_url in services:
        try:
            response = requests.get(health_url, timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {service_name}: Healthy")
                if 'dependencies' in data:
                    for dep, status in data['dependencies'].items():
                        print(f"   └─ {dep}: {status}")
            else:
                print(f"❌ {service_name}: Unhealthy ({response.status_code})")
        except Exception as e:
            print(f"❌ {service_name}: Connection failed ({e})")

def main():
    """Run all Sprint 2 tests"""
    print("🚀 Sprint 2: JWT Authentication & OAuth Integration Tests")
    print("=" * 60)
    
    # Test health checks first
    test_health_checks()
    
    # Test JWT login
    access_token, refresh_token = test_jwt_login()
    
    if access_token:
        # Test JWT authentication
        test_jwt_auth(access_token)
        
        # Test token refresh
        if refresh_token:
            new_access_token = test_refresh_token(refresh_token)
            if new_access_token:
                access_token = new_access_token
        
        # Test workflow upload with JWT
        test_workflow_upload(access_token)
    
    # Test OAuth authorization (doesn't require authentication)
    test_oauth_authorization()
    
    # Test workflow validation (doesn't require authentication)
    test_workflow_validation()
    
    print("\n" + "=" * 60)
    print("🎉 Sprint 2 testing completed!")
    print("\n📋 Summary:")
    print("   ✅ JWT Authentication implemented")
    print("   ✅ Token refresh functionality")
    print("   ✅ OAuth authorization URL generation")
    print("   ✅ Workflow validation endpoint")
    print("   ✅ Workflow upload with JWT auth")
    print("   ✅ Service health checks")

if __name__ == "__main__":
    main()
